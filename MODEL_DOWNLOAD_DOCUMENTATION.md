# Model Download in Docker

This document explains how the analysis application handles model downloads in Docker containers.

## Overview

The application uses several machine learning models for text analysis, particularly SentenceTransformer models for semantic similarity analysis. To improve startup performance and ensure reliability, these models are now downloaded during the Docker build process rather than at runtime.

## Models Downloaded

The following SentenceTransformer models are pre-downloaded during Docker build:

### 1. Korean SentenceBERT (`jhgan/ko-sbert-sts`)
- **Purpose**: Korean language sentence embeddings
- **Use Case**: Default model for Korean text analysis
- **Size**: ~400MB
- **Description**: Optimized for Korean sentence similarity tasks

### 2. Multilingual MiniLM (`paraphrase-multilingual-MiniLM-L12-v2`)
- **Purpose**: Lightweight multilingual sentence embeddings
- **Use Case**: Fast multilingual text analysis
- **Size**: ~420MB
- **Description**: Supports multiple languages with good performance/size balance

### 3. Multilingual DistilUSE (`distiluse-base-multilingual-cased-v1`)
- **Purpose**: Distilled multilingual sentence embeddings
- **Use Case**: High-quality multilingual embeddings
- **Size**: ~480MB
- **Description**: Distilled version of Universal Sentence Encoder

## Implementation Details

### Dockerfile Changes

The models are downloaded in the Dockerfile using a Python command:

```dockerfile
# Download SentenceTransformer models for sentence analysis
RUN python -c "from sentence_transformers import SentenceTransformer; \
    print('Downloading Korean SentenceBERT model...'); \
    SentenceTransformer('jhgan/ko-sbert-sts'); \
    print('Downloading Multilingual MiniLM model...'); \
    SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2'); \
    print('Downloading Multilingual DistilUSE model...'); \
    SentenceTransformer('distiluse-base-multilingual-cased-v1'); \
    print('All SentenceTransformer models downloaded successfully!')"
```

### Benefits

1. **Faster Startup**: No model download delays when starting the container
2. **Reliability**: Models are guaranteed to be available offline
3. **Consistency**: Same model versions across all deployments
4. **Caching**: Docker layer caching speeds up subsequent builds
5. **Offline Operation**: Application can run without internet access

### Build Process

1. **System Dependencies**: Install required system packages
2. **Python Dependencies**: Install Python packages from requirements.txt
3. **spaCy Models**: Download English language model
4. **SentenceTransformer Models**: Download all required embedding models
5. **Application Code**: Copy application files

## Usage

### Building the Image

```bash
# Standard build
docker build -t analysis-app .

# Using the build script
chmod +x build-docker.sh
./build-docker.sh
```

### Running the Container

```bash
# Run with port mapping
docker run -p 7000:7000 analysis-app

# Run with volume mounts for persistent data
docker run -p 7000:7000 \
  -v $(pwd)/data:/app/uploads \
  -v $(pwd)/results:/app/results \
  analysis-app
```

### Testing Model Downloads

Use the provided test script to verify model downloads work correctly:

```bash
python test_model_download.py
```

## File Structure

```
.
├── Dockerfile                     # Main Dockerfile with model downloads
├── Dockerfile.backup             # Backup of original Dockerfile
├── Dockerfile.optimized          # Optimized version with improvements
├── build-docker.sh              # Build script
├── test_model_download.py        # Test script for model downloads
└── MODEL_DOWNLOAD_DOCUMENTATION.md  # This documentation
```

## Troubleshooting

### Build Issues

1. **Network Timeouts**: Increase Docker build timeout or retry
2. **Disk Space**: Ensure sufficient disk space (~2GB for models)
3. **Memory Issues**: Increase Docker memory allocation

### Runtime Issues

1. **Model Not Found**: Rebuild the image to ensure models are downloaded
2. **Permission Issues**: Check file permissions in mounted volumes
3. **Memory Issues**: Increase container memory limits

### Verification

To verify models are properly downloaded in the container:

```bash
# Enter the container
docker run -it --rm analysis-app /bin/bash

# Check model cache directory
ls -la ~/.cache/torch/sentence_transformers/

# Test model loading
python -c "from sentence_transformers import SentenceTransformer; print(SentenceTransformer('jhgan/ko-sbert-sts'))"
```

## Performance Impact

### Build Time
- **Without Models**: ~5-10 minutes
- **With Models**: ~15-25 minutes (depending on network speed)

### Image Size
- **Without Models**: ~2GB
- **With Models**: ~3.5GB

### Runtime Performance
- **First Load**: Instant (models already cached)
- **Memory Usage**: ~1-2GB additional for loaded models
- **Startup Time**: Reduced by 30-60 seconds per model

## Future Improvements

1. **Multi-stage Build**: Use multi-stage builds to reduce final image size
2. **Model Optimization**: Quantize models to reduce size
3. **Selective Downloads**: Only download models based on configuration
4. **Model Updates**: Implement model version management
5. **Health Checks**: Add model availability health checks
