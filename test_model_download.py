#!/usr/bin/env python3
"""
Test script to verify that SentenceTransformer models can be downloaded successfully.
This script mimics what happens in the Dockerfile during the build process.
"""

import sys
import time

def download_models():
    """Download all required SentenceTransformer models"""
    try:
        from sentence_transformers import SentenceTransformer
        
        models = [
            'jhgan/ko-sbert-sts',
            'paraphrase-multilingual-MiniLM-L12-v2', 
            'distiluse-base-multilingual-cased-v1'
        ]
        
        for model_name in models:
            print(f"Downloading {model_name}...")
            start_time = time.time()
            
            # Download and initialize the model
            model = SentenceTransformer(model_name)
            
            end_time = time.time()
            print(f"✓ {model_name} downloaded successfully in {end_time - start_time:.2f} seconds")
            
            # Test encoding to ensure model works
            test_text = "This is a test sentence."
            embedding = model.encode(test_text)
            print(f"  - Test encoding successful, embedding shape: {embedding.shape}")
            
            # Clean up to save memory
            del model
            
        print("\n🎉 All SentenceTransformer models downloaded and tested successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Error: sentence-transformers package not found: {e}")
        return False
    except Exception as e:
        print(f"❌ Error downloading models: {e}")
        return False

if __name__ == "__main__":
    print("Testing SentenceTransformer model downloads...")
    print("=" * 60)
    
    success = download_models()
    
    if success:
        print("\n✅ Model download test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Model download test failed!")
        sys.exit(1)
