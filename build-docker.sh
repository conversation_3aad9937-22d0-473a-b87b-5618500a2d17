#!/bin/bash

# Docker build script for the analysis app with model pre-download
# This script builds the Docker image with all required models pre-downloaded

set -e  # Exit on any error

echo "🐳 Building Analysis App Docker Image with Pre-downloaded Models"
echo "================================================================"

# Configuration
IMAGE_NAME="analysis-app"
TAG="latest"
DOCKERFILE="Dockerfile"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Dockerfile exists
if [ ! -f "$DOCKERFILE" ]; then
    echo "❌ Error: $DOCKERFILE not found in current directory."
    exit 1
fi

echo "📋 Build Configuration:"
echo "  - Image Name: $IMAGE_NAME:$TAG"
echo "  - Dockerfile: $DOCKERFILE"
echo "  - Context: $(pwd)"
echo ""

# Start build
echo "🔨 Starting Docker build..."
echo "⏰ This may take several minutes as models are being downloaded..."
echo ""

# Build with progress output
docker build \
    --tag "$IMAGE_NAME:$TAG" \
    --file "$DOCKERFILE" \
    --progress=plain \
    .

# Check if build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Docker build completed successfully!"
    echo ""
    echo "📊 Image Information:"
    docker images "$IMAGE_NAME:$TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    echo ""
    echo "🚀 To run the container:"
    echo "  docker run -p 7000:7000 $IMAGE_NAME:$TAG"
    echo ""
    echo "🔍 To inspect the image:"
    echo "  docker run -it --rm $IMAGE_NAME:$TAG /bin/bash"
else
    echo ""
    echo "❌ Docker build failed!"
    exit 1
fi
