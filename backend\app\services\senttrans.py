from flask import Flask
import os
import uuid
import traceback
from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pandas as pd
import numpy as np
import matplotlib
# backend 설정 - Tkinter 대신 Agg 사용 (서버 환경에 적합)
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import torch
from sentence_transformers import SentenceTransformer, util
from flask import current_app
import logging


from ..services.file_processor import read_file
# 추가 패키지
try:
    import gensim
    from gensim.models import Word2Vec, FastText
    GENSIM_AVAILABLE = True
except ImportError:
    GENSIM_AVAILABLE = False
    print("Warning: gensim package not available. Word2Vec and FastText models will be limited.")

# 로깅 설정 강화
logging.basicConfig(
    level=logging.DEBUG,  # DEBUG 레벨로 변경하여 더 자세한 로그 출력
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')  # 파일에도 로그 저장
    ]
)
logger = logging.getLogger(__name__)
# Flask 앱 초기화
app = Flask(__name__)



# 전역 변수로 모델 로드 (서버 시작 시 한 번만 로드)
# 모델 로드가 오래 걸릴 수 있으므로 전역 변수로 선언
MODEL = None
MODELS = {}

# 지원하는 모델 목록 정의
AVAILABLE_MODELS = [
    {
        'id': 'jhgan/ko-sbert-sts',
        'name': 'SentenceBERT(한국어)',
        'type': 'transformer',
        'description': '한국어 문장 임베딩에 최적화된 SentenceBERT 모델'
    },
    {
        'id': 'paraphrase-multilingual-MiniLM-L12-v2',
        'name': 'MiniLM(다국어)',
        'type': 'transformer',
        'description': '다양한 언어를 지원하는 가벼운 다국어 임베딩 모델'
    },
    {
        'id': 'distiluse-base-multilingual-cased-v1',
        'name': 'DistilUSE(다국어)',
        'type': 'transformer',
        'description': '경량화된 다국어 임베딩 모델'
    },
    {
        'id': 'word2vec',
        'name': 'Word2Vec',
        'type': 'traditional',
        'description': '단어 수준의 임베딩을 위한 고전적인 모델'
    },
    {
        'id': 'fasttext',
        'name': 'FastText',
        'type': 'traditional',
        'description': '미등록 단어에도 대응 가능한 단어 임베딩 모델'
    },
    {
        'id': 'tfidf',
        'name': 'TF-IDF',
        'type': 'traditional',
        'description': '단어 빈도와 역문서 빈도를 기반으로 한 전통적인 임베딩'
    }
]

def load_model(model_id='jhgan/ko-sbert-sts'):
    """임베딩 모델 로드 함수"""
    global MODEL, MODELS

    # 이미 로드된 모델이 있으면 반환
    if model_id in MODELS and MODELS[model_id] is not None:
        logger.info(f"캐시된 모델 사용: {model_id}")
        MODEL = MODELS[model_id]
        return MODEL

    # Transformer 계열 모델
    if model_id in ['jhgan/ko-sbert-sts', 'paraphrase-multilingual-MiniLM-L12-v2', 'distiluse-base-multilingual-cased-v1']:
        try:
            logger.info(f"새 모델 로드 중: {model_id}")
            # 기존 모델 제거 (메모리 확보)
            if MODEL is not None and isinstance(MODEL, SentenceTransformer):
                del MODEL
                torch.cuda.empty_cache() if torch.cuda.is_available() else None

            MODEL = SentenceTransformer(model_id)
            MODELS[model_id] = MODEL
            return MODEL
        except Exception as e:
            logger.error(f"모델 로드 실패 {model_id}: {str(e)}")
            # 기본 모델로 폴백
            if model_id != 'jhgan/ko-sbert-sts':
                logger.info("기본 모델로 폴백")
                return load_model('jhgan/ko-sbert-sts')
            raise e

    # 전통적인 임베딩 모델들
    elif model_id == 'word2vec':
        # 간단한 Word2Vec 모델 생성 (필요할 때만 학습)
        MODEL = {'type': 'word2vec'}
        MODELS[model_id] = MODEL
        return MODEL

    elif model_id == 'fasttext':
        # 간단한 FastText 모델 생성 (필요할 때만 학습)
        MODEL = {'type': 'fasttext'}
        MODELS[model_id] = MODEL
        return MODEL

    elif model_id == 'tfidf':
        # 간단한 TF-IDF 모델 생성 (필요할 때만 학습)
        MODEL = {'type': 'tfidf'}
        MODELS[model_id] = MODEL
        return MODEL

    else:
        # 지원하지 않는 모델이면 기본 모델 반환
        logger.warning(f"지원하지 않는 모델: {model_id}, 기본 모델을 사용합니다.")
        return load_model('jhgan/ko-sbert-sts')



def analyze_word_similarity(file_path, column_name, options, session_id):
    """단어 빈도 분석 및 단어 유사도 계산 함수"""
    try:
        # 파라미터 추출
        max_features = int(options.get('max_features', 3000))
        top_n_similar = int(options.get('top_n_similar', 10))
        selection_type = options.get('selection_type', 'auto')
        visualization_shape = options.get('visualization_shape', 'circular')
        visualization_color = options.get('visualization_color', 'pastel')
        selected_words = options.get('selected_words', [])
        specific_word = options.get('specific_word', None)

        # n-gram 관련 파라미터 추출
        target_ngram = int(options.get('target_ngram', 1))

        # 모델 ID 가져오기 (기본값은 jhgan/ko-sbert-sts)
        model_id = options.get('model_id', 'jhgan/ko-sbert-sts')
        logger.info(f"분석에 사용할 모델: {model_id}")
        logger.info(f"대상 단어 N-gram: {target_ngram}")

        # 파일 읽기
        data = read_file(file_path)

        # 지정한 컬럼이 있는지 확인
        if column_name not in data.columns:
            return {"error": f"'{column_name}' 컬럼을 찾을 수 없습니다."}

        # 결과 파일용 고유 파일명 생성
        filename = f"word_sim_{uuid.uuid4().hex[:8]}"

        # 비문자열 값 처리 (NaN을 빈 문자열로 변환하고 모든 값을 문자열로 변환)
        data[column_name] = data[column_name].fillna('').astype(str)

        # 1. 단어 빈도 분석 (CountVectorizer 사용) - n-gram 설정
        cv = CountVectorizer(max_features=max_features, ngram_range=(target_ngram, target_ngram))
        tdm = cv.fit_transform(data[column_name])

        # 2. 단어 빈도 데이터프레임 생성
        word_count_tf = pd.DataFrame({
            '단어': cv.get_feature_names_out(),
            '빈도': tdm.sum(axis=0).flat
        })

        # 3. 빈도순으로 내림차순 정렬
        word_count_tf = word_count_tf.sort_values('빈도', ascending=False)
        word_count_tf = word_count_tf.reset_index(drop=True)

        # 4. 임베딩 모델 로드
        model = load_model(model_id)

        # 모델 표시 이름 설정 (모든 모드에서 사용)
        model_display_name = "Unknown Model"
        for m in AVAILABLE_MODELS:
            if m['id'] == model_id:
                model_display_name = m['name']
                break

        # --- Pairwise Specific Word Input Mode Handling ---
        if selection_type == 'pairwise_specific_input':
            word_a = options.get('specific_word_a')
            word_b = options.get('specific_word_b')

            if not word_a or not word_b:
                return {"error": "두 단어 간 유사도 비교 모드에서는 분석할 단어 A와 단어 B를 모두 입력해야 합니다."}

            # Check if the model is suitable (Transformer)
            if isinstance(model, dict) and 'type' in model:
                 return {"error": f"{model['type']} 모델은 임의의 두 단어 간 유사도 계산을 지원하지 않습니다. SentenceBERT 계열 모델을 사용해주세요."}
            elif not isinstance(model, SentenceTransformer):
                 # Fallback check if model loading somehow returned an unexpected type
                 logger.warning(f"두 단어 유사도 계산에 부적합한 모델 타입: {type(model)}. SentenceTransformer 필요.")
                 return {"error": "선택된 모델은 임의의 두 단어 간 유사도 계산을 지원하지 않습니다. SentenceBERT 계열 모델을 사용해주세요."}

            logger.info(f"Calculating similarity between specific words: '{word_a}' and '{word_b}'")

            try:
                # Encode both words
                embedding_a = model.encode([word_a], convert_to_tensor=True)
                embedding_b = model.encode([word_b], convert_to_tensor=True)

                # Calculate cosine similarity
                similarity_score = util.pytorch_cos_sim(embedding_a, embedding_b)[0][0].item()

                return {
                    "success": True,
                    "pairwise_specific_mode": True, # Flag for frontend
                    "word_a": word_a,
                    "word_b": word_b,
                    "similarity_score": f"{similarity_score:.3f}",
                    "model_used": model_id,
                    "model_display_name": model_display_name,
                    "target_ngram": "N/A", # N-gram not applicable
                    "output_file": None, # No file output for this mode
                    "image_files": []    # No images for this mode
                }
            except Exception as e:
                 logger.error(f"두 단어 ('{word_a}', '{word_b}') 유사도 계산 오류: {traceback.format_exc()}")
                 return {"error": f"입력된 단어 '{word_a}'와(과) '{word_b}'의 유사도를 계산하는 중 오류 발생: {str(e)}"}
        # --- End Pairwise Specific Word Input Mode Handling ---

        # --- Specific Word Input Mode Handling (vs Dataset) ---
        elif selection_type == 'specific_input':
            if not specific_word:
                return {"error": "특정 단어 입력 모드에서는 분석할 단어를 입력해야 합니다."}

            # Use all unique words from the column for comparison if the model supports it
            # For efficiency with large datasets, consider using only top N frequent words
            # Here, we'll use the words identified by CountVectorizer (up to max_features)
            dataset_words = cv.get_feature_names_out().tolist()
            if not dataset_words:
                 return {"error": "데이터셋에서 비교할 단어를 추출할 수 없습니다."}

            logger.info(f"Calculating similarity for specific word: '{specific_word}' against {len(dataset_words)} dataset words.")

            # Embeddings (Handle different model types)
            try:
                if isinstance(model, dict) and 'type' in model:
                    # Traditional models might not be suitable for arbitrary word similarity
                    # or require specific handling (e.g., TF-IDF only works with dataset words)
                    if model['type'] == 'tfidf':
                         return {"error": "TF-IDF 모델은 입력된 특정 단어와의 직접적인 유사도 계산을 지원하지 않습니다."}
                    # Word2Vec/FastText might work if the word is in their vocab (trained on the input data)
                    # For simplicity, we'll initially restrict this mode to Transformer models
                    # TODO: Add support for traditional models if needed
                    return {"error": f"{model['type']} 모델은 현재 특정 단어 입력 모드를 지원하지 않습니다. SentenceBERT 계열 모델을 사용해주세요."}

                # Transformer models
                specific_embedding = model.encode([specific_word], convert_to_tensor=True)
                dataset_embeddings = model.encode(dataset_words, convert_to_tensor=True)

                # Calculate similarities
                cosine_scores = util.pytorch_cos_sim(specific_embedding, dataset_embeddings)[0]
                scores_list = cosine_scores.cpu().numpy().tolist()

                # Create DataFrame of similar words from the dataset
                similar_df = pd.DataFrame({
                    '단어': dataset_words,
                    '유사도': scores_list
                })
                similar_df = similar_df.sort_values('유사도', ascending=False)

                # Get top N similar words
                top_similar = similar_df.head(top_n_similar)

                # --- Generate Excel File for Specific Word Results ---
                excel_filename = f"{filename}_specific_word_similarity.xlsx"
                excel_path = os.path.join(current_app.config['RESULT_FOLDER'], excel_filename)
                try:
                    # Prepare DataFrame for Excel output (rename columns for clarity)
                    excel_df = top_similar.copy()
                    excel_df.insert(0, '순위', range(1, len(excel_df) + 1))
                    excel_df.insert(1, '입력 단어', specific_word)
                    excel_df.rename(columns={'단어': '유사 단어 (데이터셋)', '유사도': '유사도 점수'}, inplace=True)
                    # Format similarity score
                    excel_df['유사도 점수'] = excel_df['유사도 점수'].map('{:.3f}'.format)
                    excel_df.to_excel(excel_path, index=False, engine='openpyxl')
                    logger.info(f"Specific word similarity results saved to: {excel_path}")
                except Exception as excel_e:
                    logger.error(f"Failed to save specific word results to Excel: {excel_e}")
                    # Decide if this is a critical error or just skip Excel generation
                    excel_filename = None # Indicate Excel saving failed
                # --- End Excel File Generation ---

                # Format results for the table (for direct display)
                result_records = [{
                    "순위": i + 1,
                    "입력 단어": specific_word,
                    "유사 단어 (데이터셋)": row['단어'],
                    "유사도": f"{row['유사도']:.3f}"
                } for i, row in top_similar.iterrows()]

                # Model display name
                model_display_name = "Unknown Model"
                for m in AVAILABLE_MODELS:
                    if m['id'] == model_id:
                        model_display_name = m['name']
                        break

                return {
                    "success": True,
                    "specific_word_mode": True, # Flag for frontend
                    "input_word": specific_word,
                    "similar_words_result": result_records, # Use a different key for clarity
                    "model_used": model_id,
                    "model_display_name": model_display_name,
                    "target_ngram": str(target_ngram), # Keep consistent
                    "output_file": excel_filename, # Return Excel filename
                    "image_files": []    # No images for this mode
                }

            except Exception as e:
                 logger.error(f"특정 단어 유사도 계산 오류 ({specific_word}): {traceback.format_exc()}")
                 return {"error": f"입력된 단어 '{specific_word}'에 대한 유사도를 계산하는 중 오류 발생: {str(e)}"}
        # --- End Specific Word Input Mode Handling ---

        # 5. 단어 목록 결정
        if selection_type == 'manual' and selected_words:
            # 수동 선택 모드: 선택된 단어만 사용
            word_list = selected_words
            # 이미 순위가 있는 경우 해당 순위를 유지
            word_count_selected = word_count_tf[word_count_tf['단어'].isin(word_list)]
        else:
            # 자동 선택 모드: 빈도 상위 단어 사용 (최대 100개로 제한)
            word_list = word_count_tf['단어'].head(100).tolist()
            word_count_selected = word_count_tf.head(100)

        # 모델 표시 이름 설정
        model_display_name = "SentenceBERT"
        for m in AVAILABLE_MODELS:
            if m['id'] == model_id:
                model_display_name = m['name']
                break

        # 6. 단어 임베딩 계산 (모델 타입에 따라 다른 처리)
        similar_words_with_scores = []

        # 모델 타입에 따른 임베딩 및 유사도 계산
        if isinstance(model, dict) and 'type' in model:
            # 전통적인 임베딩 모델 (Word2Vec, FastText, TF-IDF)
            model_type = model['type']

            if model_type == 'word2vec':
                # Word2Vec 모델 생성 및 학습 (간단한 구현)
                if not GENSIM_AVAILABLE:
                    logger.warning("Warning: gensim package not available. Word2Vec model will be limited.")
                    return {"error": "gensim package not available. Word2Vec model will be limited."}
                # 문장을 단어 리스트로 변환
                sentences = [text.split() for text in data[column_name] if text]
                # Word2Vec 모델 학습
                w2v_model = Word2Vec(sentences, vector_size=100, window=5, min_count=1, workers=4)

                # 각 단어별로 유사 단어 찾기
                for word in word_list:
                    # Word2Vec/FastText는 단일 단어(1-gram) 기반이므로 N-gram > 1 유사도 조회 불가
                    if target_ngram > 1:
                        similar_words_with_scores.append("N-gram 유사도 미지원")
                        continue # 다음 단어로 넘어감
                    try:
                        # 유사한 단어 찾기 (target_ngram=1 일 때만 실행)
                        similar_words = w2v_model.wv.most_similar(word, topn=top_n_similar)
                        similar_terms_with_scores = [f"{w} ({s:.2f})" for w, s in similar_words]
                        similar_words_with_scores.append(', '.join(similar_terms_with_scores))
                    except KeyError:
                        # 모델에 단어가 없는 경우
                        similar_words_with_scores.append("유사 단어를 찾을 수 없습니다")

            elif model_type == 'fasttext':
                # FastText 모델 생성 및 학습 (간단한 구현)
                if not GENSIM_AVAILABLE:
                    logger.warning("Warning: gensim package not available. FastText model will be limited.")
                    return {"error": "gensim package not available. FastText model will be limited."}
                # 문장을 단어 리스트로 변환
                sentences = [text.split() for text in data[column_name] if text]
                # FastText 모델 학습
                ft_model = FastText(sentences, vector_size=100, window=5, min_count=1, workers=4)

                # 각 단어별로 유사 단어 찾기
                for word in word_list:
                     # Word2Vec/FastText는 단일 단어(1-gram) 기반이므로 N-gram > 1 유사도 조회 불가
                    if target_ngram > 1:
                        similar_words_with_scores.append("N-gram 유사도 미지원")
                        continue # 다음 단어로 넘어감
                    try:
                         # 유사한 단어 찾기 (target_ngram=1 일 때만 실행)
                        similar_words = ft_model.wv.most_similar(word, topn=top_n_similar)
                        similar_terms_with_scores = [f"{w} ({s:.2f})" for w, s in similar_words]
                        similar_words_with_scores.append(', '.join(similar_terms_with_scores))
                    except KeyError:
                        # 모델에 단어가 없는 경우
                        similar_words_with_scores.append("유사 단어를 찾을 수 없습니다")

            elif model_type == 'tfidf':
                # TF-IDF 벡터화 (문서 단위 유사도) - 대상 단어 n-gram 설정 사용
                tfidf_vectorizer = TfidfVectorizer(max_features=max_features, ngram_range=(target_ngram, target_ngram))
                tfidf_matrix = tfidf_vectorizer.fit_transform(data[column_name])

                # 단어 인덱스 찾기
                feature_names = tfidf_vectorizer.get_feature_names_out()
                word_indices = {}

                for word in word_list:
                    if word in feature_names:
                        word_indices[word] = np.where(feature_names == word)[0][0]

                # 각 단어별로 유사 단어 찾기
                for word in word_list:
                    if word in word_indices:
                        idx = word_indices[word]
                        # 모든 단어와의 유사도 계산 (스파스 행렬이므로 0이 아닌 값들만 고려)
                        word_vector = np.zeros(len(feature_names))
                        word_vector[idx] = 1

                        similarities = cosine_similarity(tfidf_matrix[:, idx:idx+1].T, tfidf_matrix.T)[0]

                        # 유사 단어 상위 N개 선택
                        top_indices = np.argsort(similarities)[::-1][1:top_n_similar+1]  # 자기 자신 제외
                        similar_terms_with_scores = [
                            f"{feature_names[i]} ({similarities[i]:.2f})"
                            for i in top_indices if similarities[i] > 0
                        ]
                        similar_words_with_scores.append(', '.join(similar_terms_with_scores))
                    else:
                        similar_words_with_scores.append("유사 단어를 찾을 수 없습니다")
        else:
            # 트랜스포머 계열 모델 (SentenceBERT 등)
            word_embeddings = model.encode(word_list, convert_to_tensor=True)

            for idx, word in enumerate(word_list):
                # 코사인 유사도 계산
                cosine_scores = util.pytorch_cos_sim(word_embeddings[idx], word_embeddings)

                # 자기 자신을 제외한 상위 유사 단어 인덱스 추출 (내림차순 정렬)
                similar_indices = cosine_scores[0].argsort(descending=True).cpu().numpy()
                similar_indices = [i for i in similar_indices if i != idx][:top_n_similar]  # 자기 자신 제외하고 상위 N개 선택

                # 유사 단어 및 유사도 점수 저장
                similar_terms_with_scores = [
                    f"{word_list[i]} ({cosine_scores[0, i]:.2f})"
                    for i in similar_indices
                ]
                similar_words_with_scores.append(', '.join(similar_terms_with_scores))

        # 8. 유사 단어와 유사도 점수를 데이터프레임에 추가
        similar_words_df = pd.DataFrame({'단어': word_list, '유사 단어 (유사도)': similar_words_with_scores})

        # 단어를 키로 하여 두 데이터프레임 병합
        full_result = word_count_tf.copy()
        full_result = full_result.merge(similar_words_df[['단어', '유사 단어 (유사도)']], on='단어', how='left')

        # 9. 결과를 CSV 파일로 저장
        csv_filename = f"{filename}_word_similarity.csv"
        
        session_folder = os.path.join(current_app.config['RESULT_FOLDER'], session_id)
        analysis_folder = os.path.join(session_folder, "sentrans_analysis")
        csv_path = os.path.join(analysis_folder, csv_filename)
        
        full_result.to_csv(csv_path, encoding='cp949', index=False)

        # 10. 시각화할 단어 선택 (상위 10개 또는 선택된 단어 중 상위 10개)
        if selection_type == 'manual' and selected_words:
            # 수동 선택된 단어들 중에서 빈도순으로 정렬하여 상위 10개 선택
            top_words = word_count_selected.head(10)['단어'].tolist()
        else:
            # 자동 모드: 빈도 상위 10개 단어 사용
            top_words = word_count_tf.head(10)['단어'].tolist()

        # 11. 선택된 단어들에 대한 유사도 시각화
        image_files = []

        # 시각화 스타일 설정
        visualization_options = {
            'shape': visualization_shape,
            'color': visualization_color,
            'model_name': model_display_name
        }

        for word in top_words:
            # 유사도 정보가 'N-gram 유사도 미지원'이 아닌 경우에만 시각화 시도
            word_info_row = full_result.loc[full_result['단어'] == word]
            if not word_info_row.empty:
                similarity_info = word_info_row['유사 단어 (유사도)'].iloc[0]
                if pd.notna(similarity_info) and similarity_info != "N-gram 유사도 미지원":
                    image_filename = visualize_similar_words(word, full_result, filename, visualization_options)
                    if image_filename:
                        image_files.append(image_filename)

        # 12. 결과 반환
        sub_file_path = f"sentrans_analysis/{csv_filename}"
        return {
            "success": True,
            "output_file": csv_filename,
            "file_path": sub_file_path,
            "image_files": image_files,
            "top_words": full_result.head(20).to_dict('records'),  # 상위 20개 단어 결과
            "word_data": word_count_tf.head(200).apply(lambda row: {
                "word": row['단어'],
                "frequency": int(row['빈도'])
            }, axis=1).tolist(),
            "model_used": model_id,
            "model_display_name": model_display_name,
            "target_ngram": str(target_ngram) # 단일 값 반환
        }

    except Exception as e:
        logger.error(f"단어 유사도 분석 오류: {traceback.format_exc()}")
        return {"error": str(e)}

def visualize_similar_words(word, word_count_df, filename, options=None):
    """특정 단어에 대한 유사 단어와 유사도를 시각화하는 함수"""
    try:
        # 옵션 기본값 설정
        if options is None:
            options = {'shape': 'circular', 'color': 'pastel'}

        shape = options.get('shape', 'circular')
        color_theme = options.get('color', 'pastel')
        model_name = options.get('model_name', 'SentenceBERT')

        # 1. 특정 단어가 데이터프레임에 존재하는지 확인
        if word not in word_count_df['단어'].values:
            logger.error(f"'{word}' 단어가 데이터프레임에 존재하지 않습니다.")
            return None

        # 유사 단어 정보가 없는 경우 또는 미지원 메시지 처리
        similar_word_info = word_count_df.loc[word_count_df['단어'] == word, '유사 단어 (유사도)'].values[0]
        if pd.isna(similar_word_info) or similar_word_info == '' or similar_word_info == "N-gram 유사도 미지원":
            logger.warning(f"'{word}' 단어에 대한 유효한 유사 단어 정보가 없어 시각화 불가: {similar_word_info}")
            return None # 시각화하지 않음

        # 2. 특정 단어에 해당하는 유사 단어와 유사도 가져오기
        similar_word_list = similar_word_info.split(', ')

        # 3. 유사 단어와 유사도 분리하여 리스트로 만들기
        similar_words = []
        similarities = []

        # 점수 파싱 중 오류 방지
        valid_items = 0
        for item in similar_word_list:
            try:
                # Ensure item format is "term (score)"
                if ' (' in item and item.endswith(')'):
                    parts = item.rsplit(' (', 1)
                    term = parts[0]
                    score_str = parts[1].replace(')', '')
                    score = float(score_str)
                    similar_words.append(term)
                    similarities.append(score)
                    valid_items += 1
                else:
                     logger.warning(f"Skipping invalid similarity item format for '{word}': {item}")
            except ValueError:
                 logger.warning(f"Could not parse score from item for '{word}': {item}")
                 continue # Skip this item if score parsing fails

        # 유효한 유사 단어가 없는 경우 시각화 불가
        if valid_items == 0:
            logger.warning(f"No valid similar words found to visualize for '{word}' after parsing.")
            return None

        # 4. 시각화 스타일 결정
        # 각도를 단어의 개수만큼 나누기
        angles = np.linspace(0, 2 * np.pi, len(similar_words), endpoint=False).tolist()

        # 5. 그래프를 원형으로 돌려서 마지막 점이 처음과 이어지도록 추가
        angles += angles[:1]
        similarities += similarities[:1]
        labels = [f'{w}\n({s:.2f})' for w, s in zip(similar_words, similarities[:-1])]

        FONT_PATH = current_app.config['FONT_PATH']
        # 6. 한글 폰트 설정
        plt.rc('font', family=fm.FontProperties(fname=FONT_PATH).get_name())

        # 7. 그래프 생성
        fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(polar=True))

        # 8. 색상 테마 설정
        if color_theme == 'pastel':
            line_color = '#FF69B4'
            fill_color = '#FFB6C1'
            marker_color = '#FF69B4'
        elif color_theme == 'blue':
            line_color = '#1E88E5'
            fill_color = '#90CAF9'
            marker_color = '#1565C0'
        elif color_theme == 'green':
            line_color = '#43A047'
            fill_color = '#A5D6A7'
            marker_color = '#2E7D32'
        else:  # 기본 pastel
            line_color = '#FF69B4'
            fill_color = '#FFB6C1'
            marker_color = '#FF69B4'

        # 9. 플롯 추가
        ax.plot(angles, similarities, linewidth=2.5, linestyle='solid', color=line_color)
        ax.fill(angles, similarities, color=fill_color, alpha=0.5)

        # 10. 각 단어 라벨 추가
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels, fontsize=16, color='black', fontweight='bold')

        # 11. 라벨 스타일 설정
        for label, angle in zip(ax.get_xticklabels(), angles):
            label.set_horizontalalignment('center')
            label.set_rotation(np.degrees(angle) + 90)
            label.set_bbox(dict(facecolor='white', alpha=0.6, edgecolor='none', boxstyle='round,pad=0.5'))

        # 12. 그래프 배경과 그리드 스타일 설정
        ax.set_facecolor('#fafafa')
        ax.grid(color='gray', linestyle='--', linewidth=1.0, alpha=0.5)

        # 13. y축 눈금선 제거 및 장식 추가
        ax.set_yticklabels([])
        ax.spines['polar'].set_color(line_color)

        # 14. 사용자 정의 테마 적용
        ax.set_theta_offset(np.pi / 2)
        ax.set_theta_direction(-1)

        # 15. 제목 설정 - 모델 이름 포함
        plt.title(f"'{word}'의 유사 단어 ({model_name})", size=22, color='black', weight='bold', y=1.1)

        # 16. 그래프 중심점에 마커 추가
        ax.plot(0, 0, marker='o', markersize=15, color=marker_color, alpha=0.7)

        # 17. 이미지 저장
        image_filename = f"{filename}_{word}_similar_words.png"
        image_path = os.path.join(current_app.config['IMAGE_FOLDER'], image_filename)
        plt.savefig(image_path, bbox_inches='tight')
        plt.close()

        return image_filename

    except Exception as e:
        logger.error(f"유사 단어 시각화 오류: {traceback.format_exc()}")
        return None

def get_word_data(file_path, column_name, target_ngram=1):
    """단어 빈도 데이터 추출 함수 (수동 선택용)"""
    try:
        # 파일 읽기
        data = read_file(file_path)

        # 지정한 컬럼이 있는지 확인
        if column_name not in data.columns:
            return {"error": f"'{column_name}' 컬럼을 찾을 수 없습니다."}

        # 비문자열 값 처리 (NaN을 빈 문자열로 변환하고 모든 값을 문자열로 변환)
        data[column_name] = data[column_name].fillna('').astype(str)

        # 단어 빈도 분석 (CountVectorizer 사용) - n-gram 설정
        cv = CountVectorizer(max_features=3000, ngram_range=(target_ngram, target_ngram))  # n-gram 설정
        tdm = cv.fit_transform(data[column_name])

        # 단어 빈도 데이터프레임 생성
        word_count_tf = pd.DataFrame({
            '단어': cv.get_feature_names_out(),
            '빈도': tdm.sum(axis=0).flat
        })

        # 빈도순으로 내림차순 정렬
        word_count_tf = word_count_tf.sort_values('빈도', ascending=False)

        # 상위 200개 단어만 반환 (수동 선택용)
        word_data = word_count_tf.head(200).apply(
            lambda row: {'word': row['단어'], 'frequency': int(row['빈도'])},
            axis=1
        ).tolist()

        return {"success": True, "word_data": word_data}
    except Exception as e:
        logger.error(f"단어 데이터 추출 오류: {traceback.format_exc()}")
        return {"error": str(e)}

def get_file_columns(file_path):
    """파일에서 컬럼 이름 목록을 추출하는 함수"""
    try:
        data = read_file(file_path)
        return {"columns": data.columns.tolist()}
    except Exception as e:
        logger.error(f"컬럼 정보 추출 오류: {e}")
        return {"error": str(e)}
