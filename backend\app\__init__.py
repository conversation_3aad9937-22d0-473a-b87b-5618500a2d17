import os
import json
import numpy as np


from flask import Flask, jsonify, url_for
from flask_cors import CORS
from flask_swagger_ui import get_swaggerui_blueprint

from .blueprints.analyse import freq_bp as freq_blueprint, lda_bp as lda_blueprint, ngram_bp as ngram_blueprint, tfidf_bp as tfidf_blueprint, sentrans_bp as sentrans_blueprint, connet_bp as connet_blueprint, bert_bp as bert_blueprint
from .blueprints.api import api as api_blueprint
from .blueprints.data_process import process as process_blueprint
from .blueprints.upload import upload_bp as upload_blueprint
from .config import Config
from .extensions import *  # import extensions here

swaggerui_blueprint = get_swaggerui_blueprint(
    Config.SWAGGER_URL,  # Swagger UI static files will be mapped to '{SWAGGER_URL}/dist/'
    Config.API_URL,
    config={  # Swagger UI config overrides
        "app_name": "Analysis application",
        "withCredentials": True,  # Enable sending cookies with requests
    },
)



# NumPy 타입의 JSON 직렬화를 위한 사용자 정의 JSONEncoder
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (np.integer, np.int64, np.int32, np.int16, np.int8)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32, np.float16)):
            return float(obj)
        elif isinstance(obj, (np.ndarray,)):
            return obj.tolist()
        elif isinstance(obj, (np.bool_)):
            return bool(obj)
        return super(NumpyEncoder, self).default(obj)
    
def create_app(config_object="app.config.Config"):
    # Create the Flask application instance
    app = Flask(__name__)
    # NumPy 타입 처리를 위한 JSON 인코더 설정
    app.json.ensure_ascii = False
    app.json.encoder = NumpyEncoder

    # Load configuration
    app.config.from_object(config_object)

    # Get CORS allowed origins from environment variable or use a default

    # Configure CORS with more specific settings for HTTPS support
    CORS(
        app,
        resources={r"/*": {"origins": app.config["CORS_ORIGINS"]}},
        supports_credentials=True,
        allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
        methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        vary_header=True,
    )
    # Ensure upload, result, and session directories exist
    if not os.path.exists(app.config["UPLOAD_FOLDER"]):
        os.makedirs(app.config["UPLOAD_FOLDER"])

    if not os.path.exists(app.config["IMAGE_FOLDER"]):
        os.makedirs(app.config["IMAGE_FOLDER"])

    # Ensure upload, result, and session directories exist
    if not os.path.exists(app.config["PROCESSED_FOLDER"]):
        os.makedirs(app.config["PROCESSED_FOLDER"])

    # Create result folder and its subdirectories
    if not os.path.exists(app.config["RESULT_FOLDER"]):
        os.makedirs(app.config["RESULT_FOLDER"])

    # Create session folder
    if not os.path.exists(app.config["SESSION_FOLDER"]):
        os.makedirs(app.config["SESSION_FOLDER"])

    # Create session folder
    if not os.path.exists(app.config["MODEL_FOLDER"]):
        os.makedirs(app.config["MODEL_FOLDER"])
    # Initialize extensions
    # from .extensions import db, celery
    # db.init_app(app)

    # Register blueprints
    app.register_blueprint(upload_blueprint)
    app.register_blueprint(process_blueprint, url_prefix="/api")
    app.register_blueprint(freq_blueprint, url_prefix="/api/analyse")
    app.register_blueprint(lda_blueprint, url_prefix="/api/analyse")
    app.register_blueprint(tfidf_blueprint, url_prefix="/api/tfidf")
    app.register_blueprint(ngram_blueprint, url_prefix="/api/ngram")
    app.register_blueprint(sentrans_blueprint, url_prefix="/api/sentrans")
    app.register_blueprint(connet_blueprint, url_prefix="/api/connet")
    app.register_blueprint(bert_blueprint, url_prefix="/api/bert")
    # app.register_blueprint(download_blueprint, url_prefix="/api/download")
    app.register_blueprint(api_blueprint)
    app.register_blueprint(swaggerui_blueprint)

    # Add a root route that redirects to API documentation or returns basic info
    @app.route("/")
    def index():
        """Root endpoint that provides basic application information and links"""
        return jsonify(
            {
                "name": "Analysis Application",
                "version": "1.0",
                "documentation": url_for("swagger_ui.show", _external=True),
                "api_status": url_for("api.session_status", _external=True),
                "message": "Welcome to the Analysis Application API",
            }
        )

    return app

    