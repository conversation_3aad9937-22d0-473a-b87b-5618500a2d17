# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Install system dependencies in a single layer to reduce image size
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    python3-dev \
    libpq-dev \
    fonts-nanum \
    default-jdk \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy only the requirements file to leverage Docker cache
COPY requirements.txt /app/

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Download spaCy English model (this is a one-time setup, so it can be cached)
RUN python -m spacy download en_core_web_sm

# Download SentenceTransformer models for sentence analysis
# These models are downloaded during build time to avoid download delays at runtime
# Using a more robust approach with error handling
RUN python -c "
import sys
import time
from sentence_transformers import SentenceTransformer

models = [
    'jhgan/ko-sbert-sts',
    'paraphrase-multilingual-MiniLM-L12-v2', 
    'distiluse-base-multilingual-cased-v1'
]

print('Starting SentenceTransformer model downloads...')
for i, model_name in enumerate(models, 1):
    try:
        print(f'[{i}/{len(models)}] Downloading {model_name}...')
        start_time = time.time()
        model = SentenceTransformer(model_name)
        end_time = time.time()
        print(f'✓ {model_name} downloaded successfully in {end_time - start_time:.1f}s')
        del model  # Free memory
    except Exception as e:
        print(f'✗ Failed to download {model_name}: {e}')
        sys.exit(1)

print('🎉 All SentenceTransformer models downloaded successfully!')
"

# Copy the rest of the application code
COPY ./backend /app/

# Create necessary directories
#RUN mkdir -p /app/uploads /app/results /app/sessions /app/images /app/models /app/processed_uploads

# Expose the port the app runs on
EXPOSE 7000

# Set environment variables
ENV FLASK_APP=run.py
ENV FLASK_RUN_PORT=7000
ENV FLASK_ENV=production
ENV FLASK_RUN_HOST=0.0.0.0
ENV PYTHONUNBUFFERED=1

# Add a health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:7000/health || exit 1

# Command to run the application
CMD ["flask", "run", "--host=0.0.0.0", "--port=7000"]
