import os
import base64
import pandas as pd
import networkx as nx
import matplotlib
import uuid

from flask import current_app
from ..services.file_processor import read_file
from ..services.session_service import get_or_create_session_id
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import io
import traceback
import random
from itertools import combinations
from sklearn.feature_extraction.text import CountVectorizer
from scipy.spatial.distance import squareform


def connet_analysis(
    file_path,
    column_name,
    top_word_count,
    node_size_min,
    node_size_max,
    edge_width_min,
    edge_width_max,
    edge_color,
    edge_alpha,
    node_color,
    label_size,
    layout_seed,
    layout_k,
    layout_iterations,
    edge_filter_percent,
    use_concor,
    concor_n,
    concor_max_iter,
    concor_convergence,
    node_repulsion,
    group_layout_quality,
    intergroup_force,
    group_compactness,
    color_palette,
    use_gradients,
    use_custom_colors,
    custom_colors,
    label_color='#000000',
    label_font_weight='bold',
    label_background=True
):
    """Process the data and generate concor and network analysis"""
    # CONCOR algorithm implementation
    def concor(matrix, max_iter=50, convergence=0.95):
        """
        CONCOR algorithm implementation - block modeling through iterative correlation
        """
        try:
            # 행렬 크기 확인
            if len(matrix) <= 1:
                return np.array([[1]]), np.array([[1]])
                
            # Normalize matrix (0 to 1)
            if np.max(matrix) != np.min(matrix):
                norm_matrix = (matrix - np.min(matrix)) / (np.max(matrix) - np.min(matrix))
            else:
                # 모든 값이 같을 경우, 0으로 정규화
                norm_matrix = np.zeros_like(matrix)
            
            # 대각선 요소 설정 (자기 자신과의 연결)
            np.fill_diagonal(norm_matrix, 1.0)
            
            # Calculate correlation matrix - prevent NaN
            with np.errstate(invalid='ignore', divide='ignore'):
                corr_matrix = np.corrcoef(norm_matrix)
                
            # Replace NaN values with 0
            corr_matrix = np.nan_to_num(corr_matrix)
            
            # Iterate until convergence
            for i in range(max_iter):
                prev_corr = corr_matrix.copy()
                with np.errstate(invalid='ignore', divide='ignore'):
                    corr_matrix = np.corrcoef(corr_matrix)
                
                # Replace NaN values with 0
                corr_matrix = np.nan_to_num(corr_matrix)
                
                # Check for convergence - 수렴 확인 방법 개선
                if np.all(np.abs(corr_matrix) > convergence) or np.all(np.abs(corr_matrix) < (1-convergence)):
                    print(f"Convergence reached at iteration {i+1}")
                    break
                
                # Check progress - 매우 작은 변화는 수렴으로 간주
                max_diff = np.max(np.abs(corr_matrix - prev_corr))
                if max_diff < 0.001:
                    print(f"Small change detected at iteration {i+1}, diff={max_diff:.6f}")
                    break
                
                # 무한 반복 방지 - 발산 감지
                if np.isnan(corr_matrix).any() or np.isinf(corr_matrix).any():
                    print(f"Divergence detected at iteration {i+1}, using previous result")
                    corr_matrix = prev_corr
                    break
            
            # Final binary correlation matrix
            binary_matrix = np.sign(corr_matrix)
            
            return binary_matrix, corr_matrix
        except Exception as e:
            print(f"CONCOR calculation error: {e}")
            # Return safe result on error
            return np.sign(matrix), matrix

    def partition_concor(matrix, depth=1, max_depth=3, labels=None, max_iter=50, convergence=0.95):
        """
        Recursively partition using CONCOR to create 2^depth clusters
        """
        if labels is None:
            labels = {i: i for i in range(len(matrix))}
        
        # Base condition: max depth reached or matrix size is 1
        if depth > max_depth or len(matrix) <= 1:
            return {label: int(depth) for label in labels.values()}
        
        # 매우 작은 행렬에 대한 처리
        if len(matrix) <= 3:
            # 노드가 적을 경우 간단히 두 그룹으로 분할
            result = {}
            node_list = list(labels.values())
            half = len(node_list) // 2
            
            for i, node in enumerate(node_list):
                if i < half:
                    result[node] = depth * 10  # 첫 번째 그룹
                else:
                    result[node] = depth * 10 + 1  # 두 번째 그룹
            
            return result
        
        # Perform CONCOR
        try:
            binary_matrix, corr_matrix = concor(matrix, max_iter=max_iter, convergence=convergence)
            
            # Split nodes into two groups based on first row values
            indices_1 = np.where(binary_matrix[0] > 0)[0]
            indices_2 = np.where(binary_matrix[0] <= 0)[0]
            
            # If convergence to single group or failed to create two groups, force split
            if len(indices_1) == 0 or len(indices_2) == 0 or len(indices_1) < 2 or len(indices_2) < 2:
                print(f"Forcing split at depth {depth} due to convergence issues")
                
                # 더 나은 분할을 위한 방법
                # 상관관계 행렬을 사용하여 밀접하게 연결된 노드를 그룹화
                if len(corr_matrix) > 1:
                    # 계층적 클러스터링 시도
                    try:
                        from scipy.cluster.hierarchy import fcluster, linkage
                        Z = linkage(squareform(1 - np.abs(corr_matrix)), method='average')
                        clusters = fcluster(Z, 2, criterion='maxclust')
                        
                        indices_1 = np.where(clusters == 1)[0]
                        indices_2 = np.where(clusters == 2)[0]
                        
                        if len(indices_1) == 0 or len(indices_2) == 0:
                            raise ValueError("계층적 클러스터링 실패")
                            
                        print(f"Using hierarchical clustering: group1={len(indices_1)}, group2={len(indices_2)}")
                        
                    except Exception as e:
                        print(f"Hierarchical clustering failed: {e}, using degree-based split")
                        # 실패 시 노드 연결 정도에 따른 분할로 폴백
                        node_degrees = np.sum(np.abs(matrix), axis=1)
                        sorted_indices = np.argsort(node_degrees)
                        midpoint = len(sorted_indices) // 2
                        
                        indices_1 = sorted_indices[:midpoint]
                        indices_2 = sorted_indices[midpoint:]
                else:
                    # 매우 작은 행렬
                    indices_1 = np.array([0])
                    indices_2 = np.array([]) if len(matrix) <= 1 else np.array([1])
            
            # 그룹 크기 균형 확인
            if len(indices_1) > 0 and len(indices_2) > 0:
                ratio = max(len(indices_1), len(indices_2)) / (len(indices_1) + len(indices_2))
                if ratio > 0.9:  # 한 그룹이 전체의 90% 이상일 경우
                    print(f"Unbalanced split detected: {len(indices_1)} vs {len(indices_2)} nodes. Trying to balance.")
                    # 노드 연결 정도에 따른 균형있는 분할 시도
                    node_degrees = np.sum(np.abs(matrix), axis=1)
                    sorted_indices = np.argsort(node_degrees)
                    midpoint = len(sorted_indices) // 2
                    
                    indices_1 = sorted_indices[:midpoint]
                    indices_2 = sorted_indices[midpoint:]
            
            # 인덱스가 비어있는지 최종 확인
            if len(indices_1) == 0:
                indices_1 = np.array([0])
            if len(indices_2) == 0 and len(matrix) > 1:
                indices_2 = np.array([1])
            
            # Create submatrices
            mat1 = matrix[np.ix_(indices_1, indices_1)] if len(indices_1) > 0 else np.array([[0]])
            mat2 = matrix[np.ix_(indices_2, indices_2)] if len(indices_2) > 0 else np.array([[0]])
            
            # Create sublabels
            labels1 = {i: labels[indices_1[i]] for i in range(len(indices_1))} if len(indices_1) > 0 else {}
            labels2 = {i: labels[indices_2[i]] for i in range(len(indices_2))} if len(indices_2) > 0 else {}
            
            # 빈 레이블 확인
            if not labels1 and not labels2:
                return {label: int(depth) for label in labels.values()}
            
            # Recursive partitioning
            partition1 = partition_concor(mat1, depth+1, max_depth, labels1, max_iter, convergence) if labels1 else {}
            partition2 = partition_concor(mat2, depth+1, max_depth, labels2, max_iter, convergence) if labels2 else {}
            
            # Merge results
            result = {}
            result.update(partition1)
            result.update(partition2)
            
            # 누락된 노드 확인 및 처리
            for node in labels.values():
                if node not in result:
                    result[node] = int(depth)
                    
            return result
        
        except Exception as e:
            print(f"CONCOR partition error: {e}")
            # Return safe result on error
            return {label: int(depth) for label in labels.values()}

    # Main processing function
    try:
        # Load data
        data = read_file(file_path)
        original_count = len(data)
        data = data.dropna(subset=[column_name])
        actual_count = len(data)
        
        data = data.astype(str).replace(r"[\[\]',]", '', regex=True)
        
        # Extract top words
        cv = CountVectorizer(max_features=99999, ngram_range=(1, 1))
        tdm = cv.fit_transform(data[column_name])
        
        word_count_tf = pd.DataFrame({
            '단어': cv.get_feature_names_out(), 
            '빈도': tdm.sum(axis=0).flat
        })
        word_count_tf = word_count_tf.sort_values('빈도', ascending=False).reset_index(drop=True)
        
        # Check if words were extracted
        if len(word_count_tf) == 0:
            return {'error': '추출된 단어가 없습니다. 데이터를 확인하세요.'}
            
        # Select top N words
        top_word_count = min(top_word_count, len(word_count_tf))
        top_words = word_count_tf['단어'].head(top_word_count).tolist()
        
        # Create word frequency dictionary
        word_freq = dict(zip(word_count_tf['단어'], word_count_tf['빈도']))
        
        # Create co-occurrence matrix
        co_occurrence = {word: {w: 0 for w in top_words} for word in top_words}
        
        for review in data[column_name]:
            words = set(word for word in review.split() if word in top_words)
            for word1, word2 in combinations(words, 2):
                co_occurrence[word1][word2] += 1
                co_occurrence[word2][word1] += 1
                
        co_occurrence_df = pd.DataFrame(co_occurrence)
        
        # Create NetworkX graph with edge filtering
        G = nx.Graph()
        
        # Extract all edges and weights
        all_edges = []
        for word1 in top_words:
            for word2 in top_words:
                if word1 != word2:
                    weight = co_occurrence_df.at[word1, word2]
                    if weight > 0:
                        all_edges.append((word1, word2, weight))

        # Sort edges by weight (descending)
        all_edges.sort(key=lambda x: x[2], reverse=True)

        # Apply edge filtering
        if edge_filter_percent < 100:
            num_edges_to_keep = int(len(all_edges) * edge_filter_percent / 100)
            filtered_edges = all_edges[:num_edges_to_keep]
        else:
            filtered_edges = all_edges

        # Add filtered edges to graph
        for word1, word2, weight in filtered_edges:
            G.add_edge(word1, word2, weight=weight)

        # Store graph stats
        graph_stats = {
            'node_count': len(G.nodes()),
            'edge_count': len(G.edges()),
            'original_edge_count': len(all_edges),
            'filtered_edge_count': len(filtered_edges)
        }
        
        # Perform CONCOR analysis if requested
        cluster_info = {}
        if use_concor:
            # Get adjacency matrix
            adj_matrix = nx.to_numpy_array(G, nodelist=list(G.nodes()))
            
            # Node mapping for CONCOR
            node_mapping = {i: node for i, node in enumerate(G.nodes())}
            
            # Run CONCOR partitioning
            partition_labels = partition_concor(
                adj_matrix, 
                max_depth=concor_n, 
                labels=node_mapping, 
                max_iter=concor_max_iter, 
                convergence=concor_convergence
            )
            
            # Force exact number of clusters
            target_cluster_count = 2**concor_n
            unique_clusters = sorted(set(partition_labels.values()))
            current_cluster_count = len(unique_clusters)
            
            print(f"Target clusters: {target_cluster_count}, Current clusters: {current_cluster_count}")
            
            # Normalize clusters to ensure we have exactly 2^n clusters
            if current_cluster_count != target_cluster_count:
                # Create a dictionary to group nodes by cluster
                cluster_to_nodes = {}
                for node, cluster in partition_labels.items():
                    if cluster not in cluster_to_nodes:
                        cluster_to_nodes[cluster] = []
                    cluster_to_nodes[cluster].append(node)
                
                # Sort clusters by size (largest first)
                sorted_clusters = sorted(cluster_to_nodes.keys(), 
                                        key=lambda c: len(cluster_to_nodes[c]),
                                        reverse=True)
                
                # Normalize to get exactly target_cluster_count clusters
                normalized_labels = {}
                
                if current_cluster_count < target_cluster_count:
                    # We have too few clusters - split the largest ones
                    print(f"Too few clusters ({current_cluster_count}). Splitting to get {target_cluster_count}.")
                    clusters_to_create = target_cluster_count - current_cluster_count
                    new_cluster_id = max(sorted_clusters) + 1 if sorted_clusters else 0
                    
                    # 클러스터 분할 카운터
                    split_counter = 0
                    
                    # 가장 큰 클러스터부터 시도하되, 분할 가능한 클러스터를 찾음
                    for i, cluster in enumerate(sorted_clusters):
                        nodes = cluster_to_nodes[cluster]
                        
                        # 분할할 클러스터는 충분히 커야 함 (최소 4개 노드 이상)
                        if len(nodes) >= 4 and split_counter < clusters_to_create:
                            print(f"Splitting cluster {cluster} with {len(nodes)} nodes")
                            
                            try:
                                # 노드 간 연결성에 기반하여 더 지능적으로 분할
                                # 해당 클러스터의 노드로 서브그래프 생성
                                subgraph = nx.subgraph(G, nodes)
                                
                                # 노드가 충분히 많으면 스펙트럴 클러스터링 시도
                                if len(nodes) >= 10:
                                    try:
                                        import numpy as np
                                        from sklearn.cluster import SpectralClustering
                                        
                                        # 서브그래프의 인접 행렬 가져오기
                                        sub_adj = nx.to_numpy_array(subgraph)
                                        
                                        # 스펙트럴 클러스터링으로 2개 그룹으로 분할
                                        clustering = SpectralClustering(n_clusters=2, affinity='precomputed', 
                                                                        assign_labels='discretize', random_state=layout_seed)
                                        node_clusters = clustering.fit_predict(sub_adj)
                                        
                                        # 분할 결과를 적용
                                        for j, node in enumerate(nodes):
                                            if node_clusters[j] == 0:
                                                normalized_labels[node] = cluster
                                            else:
                                                normalized_labels[node] = new_cluster_id
                                                
                                        split_counter += 1
                                        new_cluster_id += 1
                                        continue
                                        
                                    except Exception as e:
                                        print(f"Spectral clustering failed: {e}, using simple split")
                                        # 실패 시 간단한 방법으로 폴백
                                
                                # 연결 중심성에 기반하여 분할
                                try:
                                    centrality = nx.degree_centrality(subgraph)
                                    sorted_nodes = sorted(nodes, key=lambda n: centrality.get(n, 0), reverse=True)
                                    midpoint = len(sorted_nodes) // 2
                                    
                                    # 분할 적용
                                    for j, node in enumerate(sorted_nodes):
                                        if j < midpoint:
                                            normalized_labels[node] = cluster
                                        else:
                                            normalized_labels[node] = new_cluster_id
                                            
                                    split_counter += 1
                                    new_cluster_id += 1
                                    
                                except Exception as e:
                                    print(f"Centrality-based split failed: {e}, using simple split")
                                    # 실패 시 간단한 중간점 분할 사용
                                    midpoint = len(nodes) // 2
                                    for j, node in enumerate(nodes):
                                        if j < midpoint:
                                            normalized_labels[node] = cluster
                                        else:
                                            normalized_labels[node] = new_cluster_id
                                            
                                    split_counter += 1
                                    new_cluster_id += 1
                                
                            except Exception as e:
                                print(f"Splitting failed: {e}, using simple split")
                                # 모든 방법 실패 시 간단한 중간점 분할
                                midpoint = len(nodes) // 2
                                for j, node in enumerate(nodes):
                                    if j < midpoint:
                                        normalized_labels[node] = cluster
                                    else:
                                        normalized_labels[node] = new_cluster_id
                                
                                split_counter += 1
                                new_cluster_id += 1
                        else:
                            # 분할하지 않는 클러스터는 그대로 유지
                            for node in nodes:
                                normalized_labels[node] = cluster
                    
                    # 모든 분할 시도 후에도 클러스터가 부족하면 인위적으로 작은 클러스터 생성
                    if split_counter < clusters_to_create:
                        print(f"Warning: Could only create {split_counter} of {clusters_to_create} needed clusters")
                        # 최종 확인: 충분한 클러스터가 생성되지 않았다면, 임의로 남은 클러스터 생성
                        # (그러나 이런 경우 시각화 결과가 이상할 수 있음)
                        if len(normalized_labels) > 0:
                            all_nodes = list(normalized_labels.keys())
                            remaining = clusters_to_create - split_counter
                            
                            # 노드가 충분히 있을 경우만 추가 클러스터 생성
                            if len(all_nodes) >= remaining:
                                for i in range(remaining):
                                    if i < len(all_nodes):
                                        normalized_labels[all_nodes[i]] = new_cluster_id + i
                                
                elif current_cluster_count > target_cluster_count:
                    # We have too many clusters - merge the smallest ones
                    print(f"Too many clusters ({current_cluster_count}). Merging to get {target_cluster_count}.")
                    
                    # 병합 전략 개선: 클러스터 크기와 함께 연결성 고려
                    try:
                        # 가장 큰 target_cluster_count-1개의 클러스터는 유지
                        clusters_to_keep = sorted_clusters[:target_cluster_count-1]
                        clusters_to_merge = sorted_clusters[target_cluster_count-1:]
                        
                        # 유지할 클러스터는 그대로 새 ID 할당
                        for i, cluster in enumerate(clusters_to_keep):
                            for node in cluster_to_nodes[cluster]:
                                normalized_labels[node] = i
                        
                        # 나머지는 마지막 클러스터로 병합
                        for cluster in clusters_to_merge:
                            for node in cluster_to_nodes[cluster]:
                                normalized_labels[node] = target_cluster_count - 1
                    
                    except Exception as e:
                        print(f"Sophisticated merge failed: {e}, using simple merge")
                        # 실패 시 간단한 방법으로 폴백
                        for i, cluster in enumerate(sorted_clusters):
                            for node in cluster_to_nodes[cluster]:
                                if i < target_cluster_count:
                                    normalized_labels[node] = i
                                else:
                                    normalized_labels[node] = target_cluster_count - 1
                
                # 누락된 노드 확인
                for node in G.nodes():
                    if node not in normalized_labels:
                        # 누락된 노드는 마지막 클러스터에 배정
                        normalized_labels[node] = 0 if target_cluster_count <= 1 else target_cluster_count - 1
                
                # Update the partition labels with normalized values
                partition_labels = normalized_labels
                
                # Verify we have the exact target number of clusters
                unique_clusters = sorted(set(partition_labels.values()))
                actual_clusters = len(unique_clusters)
                print(f"After normalization: {actual_clusters} clusters (target: {target_cluster_count})")
                
                # 최종 확인: 여전히 목표 클러스터 수와 다르면 강제 조정
                if actual_clusters != target_cluster_count:
                    print(f"Warning: Still have wrong number of clusters. Forcing {target_cluster_count} clusters.")
                    # 강제로 연속된 ID 할당
                    node_list = list(partition_labels.keys())
                    nodes_per_cluster = max(1, len(node_list) // target_cluster_count)
                    
                    for i, node in enumerate(node_list):
                        cluster_id = min(i // nodes_per_cluster, target_cluster_count - 1)
                        partition_labels[node] = cluster_id
                    
                    # 최종 클러스터 수 확인
                    unique_clusters = sorted(set(partition_labels.values()))
                    print(f"Final cluster count: {len(unique_clusters)}")
            
            # Add sequential IDs to ensure clusters are numbered 0 to N-1
            # This ensures we don't have gaps in cluster numbering which can confuse visualization
            unique_clusters = sorted(set(partition_labels.values()))
            cluster_id_mapping = {old_id: new_id for new_id, old_id in enumerate(unique_clusters)}
            
            # Apply the mapping to get sequential IDs
            for node in partition_labels:
                partition_labels[node] = cluster_id_mapping[partition_labels[node]]
                
            # Update unique clusters
            unique_clusters = sorted(set(partition_labels.values()))
            
            # Count nodes per cluster
            cluster_counts = {
                str(cluster): list(partition_labels.values()).count(cluster) 
                for cluster in unique_clusters
            }
            
            # Nodes in each cluster
            cluster_nodes = {}
            for cluster in unique_clusters:
                nodes_in_cluster = [node for node, c in partition_labels.items() if c == cluster]
                cluster_nodes[str(cluster)] = nodes_in_cluster
            
            # Add cluster attribute to nodes
            for node in G.nodes():
                G.nodes[node]['group'] = partition_labels.get(node, 0)  # 기본값 추가
            
            # Select appropriate colormap or use custom colors
            if use_custom_colors and custom_colors:
                # Create a custom colormap from user-provided colors
                node_colors_hex = []
                for node in G.nodes():
                    group = G.nodes[node]['group']
                    # If group has a custom color, use it; otherwise, use default
                    if group in custom_colors:
                        node_colors_hex.append(custom_colors[group])
                    else:
                        # 사용자 정의 색상이 없을 경우 안전한 대체 색상 사용
                        default_colors = ['#4C78A8', '#F58518', '#E45756', '#72B7B2', 
                                          '#54A24B', '#EECA3B', '#B279A2', '#FF9DA6', 
                                          '#9D755D', '#BAB0AC']
                        color_index = group % len(default_colors)
                        node_colors_hex.append(default_colors[color_index])
            else:
                try:
                    # Select appropriate colormap based on user choice
                    colormap_name = color_palette
                    if colormap_name == 'tableau10':
                        colormap = plt.cm.tab10
                    elif colormap_name == 'category10':
                        colormap = plt.cm.tab10
                    elif colormap_name == 'set3':
                        colormap = plt.cm.Set3
                    elif colormap_name == 'spectral':
                        colormap = plt.cm.Spectral
                    elif colormap_name == 'dark2':
                        colormap = plt.cm.Dark2
                    elif colormap_name == 'pastel1':
                        colormap = plt.cm.Pastel1
                    else:
                        colormap = plt.cm.tab10
                    
                    # Get RGBA colors for each node with 예외 처리
                    node_colors = []
                    for node in G.nodes():
                        group = G.nodes[node]['group']
                        # 그룹 번호가 음수인 경우 방지
                        if group < 0:
                            group = 0
                        # 유효한 인덱스 확보
                        color_idx = group % colormap.N
                        node_colors.append(colormap(color_idx))
                    
                    # Convert RGBA to hex for JSON (예외 처리 추가)
                    node_colors_hex = []
                    for rgba in node_colors:
                        try:
                            hex_color = matplotlib.colors.rgb2hex(rgba)
                            node_colors_hex.append(hex_color)
                        except Exception as e:
                            print(f"Color conversion error: {e}, using default")
                            node_colors_hex.append('#CCCCCC')  # 회색 기본값
                
                except Exception as e:
                    print(f"Colormap error: {e}, using safe colors")
                    # 모든 색상 관련 코드가 실패할 경우 안전한 대체 색상 사용
                    node_colors_hex = []
                    default_colors = ['#4C78A8', '#F58518', '#E45756', '#72B7B2', 
                                     '#54A24B', '#EECA3B', '#B279A2', '#FF9DA6']
                    
                    for node in G.nodes():
                        group = G.nodes[node]['group']
                        color_index = group % len(default_colors)
                        node_colors_hex.append(default_colors[color_index])
            
            # 색상 배열 길이 확인 (오류 방지)
            if len(node_colors_hex) != len(G.nodes()):
                print(f"Warning: Color array length mismatch. Expected {len(G.nodes())}, got {len(node_colors_hex)}")
                # 색상 배열 수정
                if len(node_colors_hex) < len(G.nodes()):
                    # 부족한 색상 추가
                    default_color = '#CCCCCC'
                    node_colors_hex.extend([default_color] * (len(G.nodes()) - len(node_colors_hex)))
                else:
                    # 초과 색상 제거
                    node_colors_hex = node_colors_hex[:len(G.nodes())]
            
            # Save cluster information
            cluster_info = {
                'unique_clusters': len(unique_clusters),
                'expected_clusters': 2 ** concor_n,
                'cluster_counts': cluster_counts,
                'cluster_nodes': cluster_nodes,
                'node_clusters': {node: partition_labels.get(node, 0) for node in G.nodes()}  # 기본값 추가
            }
        else:
            # Use default color or gradient for all nodes if CONCOR not used
            if use_gradients:
                # Create gradient based on word frequency
                node_words = list(G.nodes())
                word_freqs = [word_freq.get(word, 1) for word in node_words]
                min_freq = min(word_freqs)
                max_freq = max(word_freqs)
                
                # Create a gradient from blue to red
                cmap = plt.cm.cool
                
                node_colors_hex = []
                for word in G.nodes():
                    freq = word_freq.get(word, 1)
                    if max_freq == min_freq:
                        normalized = 0.5
                    else:
                        normalized = (freq - min_freq) / (max_freq - min_freq)
                    color = cmap(normalized)
                    node_colors_hex.append(matplotlib.colors.rgb2hex(color))
                
                # Set all nodes to same group
                for node in G.nodes():
                    G.nodes[node]['group'] = 0
            else:
                # Use default color for all nodes
                node_colors_hex = [node_color] * len(G.nodes())
                # Set all nodes to same group
                for node in G.nodes():
                    G.nodes[node]['group'] = 0
        
        # Calculate node sizes based on word frequency
        node_words = list(G.nodes())
        word_freqs = [word_freq.get(word, 1) for word in node_words]
        
        # Map word frequencies to node sizes
        node_sizes = []
        if word_freqs:
            min_freq = min(word_freqs)
            max_freq = max(word_freqs)
            
            for freq in word_freqs:
                if max_freq == min_freq:  # All frequencies are the same
                    size = (node_size_min + node_size_max) / 2
                else:
                    normalized = (freq - min_freq) / (max_freq - min_freq)
                    size = node_size_min + normalized * (node_size_max - node_size_min)
                node_sizes.append(size)
        
        # Calculate edge widths based on weights
        edges = G.edges(data=True)
        weights = [d['weight'] for (_, _, d) in edges]
        
        edge_weights = []
        if weights:
            min_weight = min(weights)
            max_weight = max(weights)
            
            for weight in weights:
                if max_weight == min_weight:  # All weights are the same
                    edge_width = (edge_width_min + edge_width_max) / 2
                else:
                    normalized = (weight - min_weight) / (max_weight - min_weight)
                    edge_width = edge_width_min + normalized * (edge_width_max - edge_width_min)
                edge_weights.append(edge_width)
        
        # Generate layout
        if use_concor:
            # Create initial layout
            init_pos = nx.spring_layout(G, k=layout_k, iterations=50, seed=layout_seed)
            
            # Create subgraphs for each cluster
            subgraphs = {}
            unique_clusters = sorted(set(nx.get_node_attributes(G, 'group').values()))
            
            for cluster in unique_clusters:
                nodes_in_cluster = [node for node in G.nodes() if G.nodes[node]['group'] == cluster]
                subgraphs[cluster] = G.subgraph(nodes_in_cluster)
            
            # Calculate layout for each subgraph
            subgraph_pos = {}
            for cluster, subgraph in subgraphs.items():
                if len(subgraph) > 1:
                    sub_pos = nx.spring_layout(
                        subgraph, 
                        k=layout_k * group_compactness, 
                        iterations=group_layout_quality, 
                        seed=layout_seed+cluster
                    )
                    subgraph_pos.update(sub_pos)
                else:
                    for node in subgraph:
                        subgraph_pos[node] = init_pos[node]
                                    
            # Calculate cluster centers
            cluster_centers = {}
            for cluster in unique_clusters:
                nodes = [node for node in G.nodes() if G.nodes[node]['group'] == cluster]
                if nodes:
                    x_coords = [init_pos[node][0] for node in nodes if node in init_pos]
                    y_coords = [init_pos[node][1] for node in nodes if node in init_pos]
                    
                    # Only calculate center if we have valid coordinates
                    if x_coords and y_coords:
                        cluster_centers[cluster] = (np.mean(x_coords), np.mean(y_coords))
                    else:
                        # Fallback to origin if no valid coordinates
                        cluster_centers[cluster] = (0.0, 0.0)
                else:
                    # Fallback for empty clusters
                    cluster_centers[cluster] = (0.0, 0.0)
            
            # Expand centers to separate clusters
            expanded_centers = cluster_centers.copy() # 초기 센터 위치를 그대로 사용
            
            # Apply intergroup forces to separate clusters more
            if len(unique_clusters) > 1 and intergroup_force > 0.1: # 최소 힘 적용 조건 조정
                for _ in range(20):
                    forces = {cluster: [0, 0] for cluster in unique_clusters}
                    
                    for c1 in unique_clusters:
                        for c2 in unique_clusters:
                            if c1 >= c2:
                                continue
                                
                            x1, y1 = expanded_centers[c1]
                            x2, y2 = expanded_centers[c2]
                            dx = x2 - x1
                            dy = y2 - y1
                            dist_sq = dx**2 + dy**2
                            
                            if dist_sq < 0.0001:
                                angle = random.uniform(0, 2*np.pi)
                                dx = np.cos(angle) * 0.01
                                dy = np.sin(angle) * 0.01
                                dist_sq = 0.0001
                            
                            dist = np.sqrt(dist_sq)
                            
                            # 인력(음수)과 척력(양수) 계산
                            # intergroup_force 값이 클수록 척력이 강해짐
                            # target_dist = 1.0 # 그룹 간 목표 거리 (조절 가능)
                            # force_magnitude = intergroup_force * (dist - target_dist) / dist 
                            force_magnitude = intergroup_force / dist # 단순 반비례 척력
                            
                            fx = dx / dist * force_magnitude
                            fy = dy / dist * force_magnitude
                            
                            forces[c1][0] -= fx
                            forces[c1][1] -= fy
                            forces[c2][0] += fx
                            forces[c2][1] += fy
                    
                    # Update centers based on forces
                    for cluster in unique_clusters:
                        fx, fy = forces[cluster]
                        x, y = expanded_centers[cluster]
                        # 이동량 제한 (너무 빠르게 발산하지 않도록)
                        move_dist = np.sqrt(fx**2 + fy**2)
                        max_move = 0.1 # 최대 이동 거리
                        if move_dist > max_move:
                            fx = fx * max_move / move_dist
                            fy = fy * max_move / move_dist
                            
                        expanded_centers[cluster] = (
                            x + fx * 0.1, # 학습률 조절
                            y + fy * 0.1
                        )
            
            # Calculate final node positions
            pos = {}
            for node in G.nodes():
                cluster = G.nodes[node]['group']
                
                if node in subgraph_pos:
                    rel_x, rel_y = subgraph_pos[node]
                    
                    init_center_x, init_center_y = cluster_centers[cluster]
                    dx = rel_x - init_center_x
                    dy = rel_y - init_center_y
                    
                    exp_center_x, exp_center_y = expanded_centers[cluster]
                    pos[node] = (
                        exp_center_x + dx * node_repulsion, 
                        exp_center_y + dy * node_repulsion
                    )
                else:
                    pos[node] = init_pos[node]
        else:
            # Use basic spring layout
            pos = nx.spring_layout(G, k=layout_k, iterations=layout_iterations, seed=layout_seed)
        
        # Prepare graph data for JSON
        graph_data = {
            'nodes': [],
            'links': []
        }
        
        # Add nodes with attributes
        for i, node in enumerate(G.nodes()):
            graph_data['nodes'].append({
                'id': node,
                'label': node,
                'size': float(node_sizes[i]),
                'color': node_colors_hex[i],
                'x': float(pos[node][0]),
                'y': float(pos[node][1]),
                'group': int(G.nodes[node]['group']),
                'freq': int(word_freq.get(node, 0))
            })
        
        # Add edges with attributes
        for i, (source, target, data) in enumerate(G.edges(data=True)):
            graph_data['links'].append({
                'source': source,
                'target': target,
                'weight': int(data['weight']),
                'width': float(edge_weights[i]),
                'color': edge_color,
                'alpha': edge_alpha
            })
        
        # Store metadata
        metadata = {
            'file_stats': {
                'original_count': original_count,
                'processed_count': actual_count,
                'missing_count': original_count - actual_count
            },
            'word_stats': {
                'total_words': len(word_count_tf),
                'top_words': top_word_count
            },
            'graph_stats': graph_stats,
            'layout_info': {
                'seed': layout_seed,
                'k': layout_k,
                'iterations': layout_iterations
            },
            'cluster_info': cluster_info
        }
        
        # Generate a static version of the visualization for download
        # 한글 폰트 설정
        try:
            # 윈도우용 폰트 설정 
            if os.name == 'nt':
                plt.rcParams['font.family'] = ['Malgun Gothic', 'Arial']
            # macOS용 폰트 설정
            elif os.name == 'posix':
                plt.rcParams['font.family'] = ['AppleGothic', 'Arial']
        except Exception as e:
            print(f"폰트 설정 중 오류: {e}")
            plt.rcParams['font.family'] = 'Arial'
            
        plt.rcParams['axes.unicode_minus'] = False
        
        plt.figure(figsize=(20, 16), facecolor='white', dpi=100)
        
        # Draw edges
        nx.draw_networkx_edges(
            G, pos, 
            width=edge_weights, 
            alpha=edge_alpha, 
            edge_color=edge_color
        )
        
        # Draw nodes
        nx.draw_networkx_nodes(
            G, pos, 
            node_color=node_colors_hex, 
            node_size=node_sizes, 
            alpha=0.85, 
            edgecolors='white', 
            linewidths=1.5
        )
        
        # 라벨 스타일 적용
        label_options = {
            'font_size': label_size,
            'font_weight': label_font_weight,
            'font_color': label_color
        }
        
        # 라벨 배경 사용 여부
        if label_background:
            label_options['bbox'] = dict(
                boxstyle="round,pad=0.3",
                fc="white",
                ec="none",
                alpha=0.7
            )
        
        # Draw labels with improved readability
        nx.draw_networkx_labels(
            G, pos, 
            **label_options
        )
        
        plt.axis('off')
        plt.tight_layout()

        # Generate unique filename for this analysis
        session_id = get_or_create_session_id()
        analysis_id = uuid.uuid4().hex[:8]

        # Create analysis folder
        result_folder = current_app.config["RESULT_FOLDER"]
        analysis_folder = os.path.join(result_folder, session_id, "concor_analysis")
        os.makedirs(analysis_folder, exist_ok=True)

        # Save network visualization image to disk
        image_filename = f"concor_network_{analysis_id}.png"
        image_path = os.path.join(analysis_folder, image_filename)
        plt.savefig(image_path, format='png', dpi=200, bbox_inches='tight')

        # Save the figure to a buffer for base64 (maintain existing functionality)
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=200, bbox_inches='tight')
        plt.close()
        buf.seek(0)

        # Encode the image as base64
        image_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')

        # Save graph data to JSON file
        json_filename = f"concor_graph_data_{analysis_id}.json"
        json_path = os.path.join(analysis_folder, json_filename)
        with open(json_path, 'w', encoding='utf-8') as f:
            import json
            json.dump(graph_data, f, ensure_ascii=False, indent=2)

        # Save metadata to Excel file
        excel_filename = f"concor_metadata_{analysis_id}.xlsx"
        excel_path = os.path.join(analysis_folder, excel_filename)

        # Create metadata DataFrame for Excel export
        metadata_df = pd.DataFrame([metadata])
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            metadata_df.to_excel(writer, sheet_name='Analysis_Metadata', index=False)

            # Add cluster information if available
            if cluster_info:
                cluster_df = pd.DataFrame(list(cluster_info.items()), columns=['Node', 'Cluster'])
                cluster_df.to_excel(writer, sheet_name='Cluster_Info', index=False)

        # Create file paths relative to session folder
        image_sub_path = f"concor_analysis/{image_filename}"
        json_sub_path = f"concor_analysis/{json_filename}"
        excel_sub_path = f"concor_analysis/{excel_filename}"

        # Return results
        return {
            'graph': graph_data,
            'metadata': metadata,
            'image': image_base64,
            'image_file_path': image_sub_path,
            'json_file_path': json_sub_path,
            'excel_file_path': excel_sub_path
        }

    except Exception as e:
        print(traceback.format_exc())
        return {'error': str(e), 'traceback': traceback.format_exc()}