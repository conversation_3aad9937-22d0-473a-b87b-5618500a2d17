from flask import request, jsonify, url_for
import traceback

from ...services.json_session_service import get_session_value

from ...services.session_service import get_or_create_session_id
from ...services.nlp_service import (analyze_topics, get_file_path, read_file)

from . import bert_bp

@bert_bp.route('/analyze', methods=['POST'])
def analyze():
    try:
        session_id = get_or_create_session_id()
        
        
        column_name = request.form.get('column_name', '')
        if not column_name:
            return jsonify({'error': '분석할 컬럼이 선택되지 않았습니다.'})
        
        # 모델 파라미터 가져오기
        params = {
            'nr_topics': request.form.get('nr_topics', "auto"),
            'top_n_words': request.form.get('top_n_words', "10"),
            'min_topic_size': request.form.get('min_topic_size', "5"),
            'max_ngram': request.form.get('max_ngram', "1"),
            'min_df': request.form.get('min_df', "2"),
            'embedding_model': request.form.get('embedding_model', 
                              "sentence-transformers/xlm-r-100langs-bert-base-nli-stsb-mean-tokens")
        }
        
        # 파일 읽기 시도 (오류 처리 강화)
        filename = get_session_value(session_id, "uploaded_file")
        file_path = get_file_path(session_id, filename)
        try:
            df = read_file(file_path)
        except Exception as e:
            return jsonify({'error': f'파일 읽기 실패: {str(e)}'})
        
        # 데이터 확인
        print(f"파일 읽기 완료. shape: {df.shape}")
        
        if column_name not in df.columns:
            return jsonify({'error': '선택한 컬럼이 존재하지 않습니다.'})
        
        # 데이터 검증
        if df[column_name].isnull().all() or df[column_name].astype(str).str.strip().eq("").all():
            return jsonify({'error': '선택한 컬럼에 분석 가능한 텍스트 데이터가 없습니다.'})
        
        # 분석 수행
        try:
            result = analyze_topics(df, column_name, params)
        except ValueError as ve:
            return jsonify({'error': str(ve)})
        except Exception as e:
            error_details = str(e)
            if "list index out of range" in error_details:
                return jsonify({'error': '토픽 추출에 실패했습니다. 데이터 크기가 너무 작거나 텍스트 내용이 부적절할 수 있습니다. 다른 설정이나 데이터를 시도해보세요.'})
            return jsonify({'error': f'분석 중 오류가 발생했습니다: {error_details}'})
        
        print("분석 완료, 결과 반환")

        # Add download URLs for all result files
        if 'file_path' in result:
            result["excel_download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                ** result
            }
        )
            
    except Exception as e:
        print(f"에러 발생: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'분석 중 오류가 발생했습니다: {str(e)}'})

# @bert_bp.route('/download/<path:filename>', methods=['GET'])
# def download_file(filename):
#     directory = os.path.join(app.root_path, 'static', 'results')
#     return send_from_directory(directory, filename, as_attachment=True)

# if __name__ == '__main__':
#     app.run(debug=False, threaded=True, host='0.0.0.0', port=5004) 